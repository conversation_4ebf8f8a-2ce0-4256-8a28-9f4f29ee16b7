﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Assign changed successfully" xml:space="preserve">
    <value>L'attribution a été modifiée avec succès</value>
  </data>
  <data name="Assigned successfully" xml:space="preserve">
    <value>Attribué avec succès</value>
  </data>
  <data name="Create a new role" xml:space="preserve">
    <value>Créer un nouveau rôle</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Edit the role" xml:space="preserve">
    <value>Modifier le rôle</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Rôles</value>
  </data>
  <data name="Search by role name" xml:space="preserve">
    <value>Rechercher un nom de rôle</value>
  </data>
  <data name="Set Permissions" xml:space="preserve">
    <value>Définir les autorisations</value>
  </data>
  <data name="Unassigned successfully" xml:space="preserve">
    <value>Non attribué avec succès</value>
  </data>
  <data name="Assigned Roles" xml:space="preserve">
    <value>Rôles assignés</value>
  </data>
  <data name="Email / PhoneNumber" xml:space="preserve">
    <value>E-mail/numéro de téléphone</value>
  </data>
  <data name="Selected" xml:space="preserve">
    <value>Selectionné(s)</value>
  </data>
  <data name="Permission assigned successfully" xml:space="preserve">
    <value>Autorisation attribuée avec succès</value>
  </data>
  <data name="Permission removed successfully" xml:space="preserve">
    <value>Autorisation supprimée avec succès</value>
  </data>
  <data name="Authorization has been changed" xml:space="preserve">
    <value>L'autorisation a été modifiée</value>
  </data>
  <data name="Assign or Unassign all" xml:space="preserve">
    <value>Tout attribuer ou désattribuer</value>
  </data>
  <data name="Permission" xml:space="preserve">
    <value>Autorisation</value>
  </data>
  <data name="Permission Description" xml:space="preserve">
    <value>Description de l'autorisation</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Des produits</value>
  </data>
  <data name="Products Permissions" xml:space="preserve">
    <value>Autorisations des produits</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Catégories</value>
  </data>
  <data name="Categories Permissions" xml:space="preserve">
    <value>Catégories Autorisations</value>
  </data>
  <data name="Documents" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="Documents Permissions" xml:space="preserve">
    <value>Autorisations de documents</value>
  </data>
  <data name="Customers" xml:space="preserve">
    <value>Clients</value>
  </data>
  <data name="Customers Permissions" xml:space="preserve">
    <value>Autorisations des clients</value>
  </data>
  <data name="Audit Trails" xml:space="preserve">
    <value>Des pistes de vérification</value>
  </data>
  <data name="Audit Trails Permissions" xml:space="preserve">
    <value>Autorisations des pistes d'audit</value>
  </data>
  <data name="Logs" xml:space="preserve">
    <value>Journaux</value>
  </data>
  <data name="Logs Permissions" xml:space="preserve">
    <value>Autorisations de journaux</value>
  </data>
  <data name="Picklist Permissions" xml:space="preserve">
    <value>Autorisations de liste de sélection</value>
  </data>
  <data name="Picklist" xml:space="preserve">
    <value>Liste de sélection</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="Users Permissions" xml:space="preserve">
    <value>Autorisations des utilisateurs</value>
  </data>
  <data name="Roles Permissions" xml:space="preserve">
    <value>Autorisations des rôles</value>
  </data>
  <data name="Multi-Tenant" xml:space="preserve">
    <value>Multi-locataire</value>
  </data>
  <data name="Multi-Tenant Permissions" xml:space="preserve">
    <value>Autorisations multi-locataires</value>
  </data>
  <data name="Role Claims" xml:space="preserve">
    <value>Revendications de rôle</value>
  </data>
  <data name="Role Claims Permissions" xml:space="preserve">
    <value>Autorisations de revendications de rôle</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Tableau de bord</value>
  </data>
  <data name="Dashboard Permissions" xml:space="preserve">
    <value>Autorisations du tableau de bord</value>
  </data>
  <data name="Job" xml:space="preserve">
    <value>Emploi</value>
  </data>
  <data name="Job Permissions" xml:space="preserve">
    <value>Autorisations de travail</value>
  </data>
  <data name="Normalized Name" xml:space="preserve">
    <value>Nom normalisé</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>Identifiant</value>
  </data>
  <data name="Tenant Name" xml:space="preserve">
    <value>Nom du locataire</value>
  </data>
</root>
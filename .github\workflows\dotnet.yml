# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: Build

on:
  push:
    branches: [ main ]
    paths-ignore:
      - "deploy/**"
  pull_request:
    branches: [ main ]
    paths-ignore:
      - "deploy/**"

jobs:
  build:

    runs-on: ubuntu-latest
          
    steps:
    - uses: actions/checkout@v4
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x

    - name: Restore dependencies
      run: dotnet restore CleanArchitecture.Blazor.sln
    - name: Build
      run: dotnet build CleanArchitecture.Blazor.sln --configuration Debug --no-restore
   
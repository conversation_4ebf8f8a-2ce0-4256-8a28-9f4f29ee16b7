<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
        Microsoft ResX Schema 
        
        Version 2.0
        
        The primary goals of this format is to allow a simple XML format 
        that is mostly human readable. The generation and parsing of the 
        various data types are done through the TypeConverter classes 
        associated with the data types.
        
        Example:
        
        ... ado.net/XML headers & schema ...
        <resheader name="resmimetype">text/microsoft-resx</resheader>
        <resheader name="version">2.0</resheader>
        <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
        <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
        <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
        <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
        <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
            <value>[base64 mime encoded serialized .NET Framework object]</value>
        </data>
        <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
            <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
            <comment>This is a comment</comment>
        </data>
                    
        There are any number of "resheader" rows that contain simple 
        name/value pairs.
        
        Each data row contains a name, and value. The row also contains a 
        type or mimetype. Type corresponds to a .NET class that support 
        text/value conversion through the TypeConverter architecture. 
        Classes that don't support this are serialized and stored with the 
        mimetype set.
        
        The mimetype is used for serialized objects, and tells the 
        ResXResourceReader how to depersist the object. This is currently not 
        extensible. For a given mimetype the value must be set accordingly:
        
        Note - application/x-microsoft.net.object.binary.base64 is the format 
        that the ResXResourceWriter will generate, however the reader can 
        read any of the formats listed below.
        
        mimetype: application/x-microsoft.net.object.binary.base64
        value   : The object must be serialized with 
                : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
                : and then encoded with base64 encoding.
        
        mimetype: application/x-microsoft.net.object.soap.base64
        value   : The object must be serialized with 
                : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
                : and then encoded with base64 encoding.
    
        mimetype: application/x-microsoft.net.object.bytearray.base64
        value   : The object must be serialized into a byte array 
                : using a System.ComponentModel.TypeConverter
                : and then encoded with base64 encoding.
        -->
  <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root" xmlns="">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
  </resheader>
  <data name="Don't have an account?" xml:space="preserve">
    <value>Sie haben kein Konto?</value>
  </data>
  <data name="Forgot password?" xml:space="preserve">
    <value>Passwort vergessen?</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="Password is required!" xml:space="preserve">
    <value>Passwort wird benötigt!</value>
  </data>
  <data name="Password must be at least of length 6" xml:space="preserve">
    <value>Das Passwort muss mindestens die Länge 6 haben</value>
  </data>
  <data name="Password must contain at least one capital letter" xml:space="preserve">
    <value>Das Passwort muss mindestens einen Großbuchstaben enthalten</value>
  </data>
  <data name="Password must contain at least one digit" xml:space="preserve">
    <value>Das Passwort muss mindestens eine Ziffer enthalten</value>
  </data>
  <data name="Password must contain at least one lowercase letter" xml:space="preserve">
    <value>Das Passwort muss mindestens einen Kleinbuchstaben enthalten</value>
  </data>
  <data name="Please check your username and password. If you are still unable to log in, contact an administrator." xml:space="preserve">
    <value>Bitte überprüfen Sie Ihren Benutzernamen und Ihr Passwort. Wenn Sie sich immer noch nicht anmelden können, wenden Sie sich an einen Administrator.</value>
  </data>
  <data name="Remember me?" xml:space="preserve">
    <value>Erinnere dich an mich?</value>
  </data>
  <data name="Sign In" xml:space="preserve">
    <value>Anmelden</value>
  </data>
  <data name="Sign Up" xml:space="preserve">
    <value>Melden Sie sich an</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Nutzername</value>
  </data>
  <data name="Username is required!" xml:space="preserve">
    <value>Benutzername wird benötigt!</value>
  </data>
  <data name="No user found, or no authorization, please contact an administrator." xml:space="preserve">
    <value>Kein Benutzer gefunden, oder keine Berechtigung, bitte kontaktieren Sie einen Administrator.</value>
  </data>
  <data name="User name is required!" xml:space="preserve">
    <value>Benutzername wird benötigt!</value>
  </data>
  <data name="Please check your username and password. If you are still unable to log in, contact your administrator." xml:space="preserve">
    <value>Bitte überprüfen Sie Ihren Benutzernamen und Ihr Passwort. Wenn Sie sich immer noch nicht anmelden können, wenden Sie sich an Ihren Administrator.</value>
  </data>
  <data name="No user found, or no authorization, please contact the administrator." xml:space="preserve">
    <value>Kein Benutzer gefunden, oder keine Berechtigung, bitte kontaktieren Sie einen Administrator.</value>
  </data>
  <data name="User name" xml:space="preserve">
    <value>Nutzername</value>
  </data>
  <data name="Please check your username and password.  If you are still unable to log in, contact your administrator." xml:space="preserve">
    <value>Bitte überprüfen Sie Ihren Benutzernamen und Ihr Passwort. Wenn Sie sich immer noch nicht anmelden können, wenden Sie sich an Ihren Administrator.</value>
  </data>
  <data name="Password update successfully" xml:space="preserve">
    <value>Passwortaktualisierung erfolgreich</value>
  </data>
  <data name="Set new password" xml:space="preserve">
    <value>Neues Passwort festlegen</value>
  </data>
  <data name="Error: Invalid login attempt." xml:space="preserve">
    <value>Fehler: Ungültiger Anmeldeversuch.</value>
  </data>
  <data name="Error: Your account is not allowed to log in. Please ensure your account has been activated and you have completed all required steps." xml:space="preserve">
    <value>Fehler: Ihr Konto darf sich nicht anmelden. Bitte stellen Sie sicher, dass Ihr Konto aktiviert wurde und Sie alle erforderlichen Schritte abgeschlossen haben.</value>
  </data>
  <data name="Error: User does not exist." xml:space="preserve">
    <value>Fehler: Benutzer existiert nicht.</value>
  </data>
  <data name="Error: Your account is inactive. Please contact support." xml:space="preserve">
    <value>Fehler: Ihr Konto ist inaktiv. Bitte wenden Sie sich an den Support.</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Assign Roles" xml:space="preserve">
    <value>Attribuer des rôles</value>
  </data>
  <data name="Click upload a photo." xml:space="preserve">
    <value>Cliquez sur télécharger une photo.</value>
  </data>
  <data name="Confirm New Password" xml:space="preserve">
    <value>Confirmer le nouveau mot de passe</value>
  </data>
  <data name="confirm password is required!" xml:space="preserve">
    <value>confirmer que le mot de passe est requis !</value>
  </data>
  <data name="Create a new user" xml:space="preserve">
    <value>Créer un nouvel utilisateur</value>
  </data>
  <data name="Edit the user" xml:space="preserve">
    <value>Modifier l'utilisateur</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="Is Active" xml:space="preserve">
    <value>C'est actif</value>
  </data>
  <data name="Lock Status" xml:space="preserve">
    <value>Statut de verrouillage</value>
  </data>
  <data name="New Password" xml:space="preserve">
    <value>nouveau mot de passe</value>
  </data>
  <data name="password is required!" xml:space="preserve">
    <value>Mot de passe requis!</value>
  </data>
  <data name="Phone Number" xml:space="preserve">
    <value>Numéro de téléphone</value>
  </data>
  <data name="Reset password successfully." xml:space="preserve">
    <value>Réinitialiser le mot de passe avec succès</value>
  </data>
  <data name="Search by user name" xml:space="preserve">
    <value>Rechercher un nom d'utilisateur</value>
  </data>
  <data name="Select Tenant" xml:space="preserve">
    <value>Sélectionner un locataire</value>
  </data>
  <data name="Set Active" xml:space="preserve">
    <value>Définir actif</value>
  </data>
  <data name="Set Inactive" xml:space="preserve">
    <value>Définir comme inactif</value>
  </data>
  <data name="Set Password" xml:space="preserve">
    <value>Définir le mot de passe</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Site</value>
  </data>
  <data name="Tenant Name" xml:space="preserve">
    <value>Nom du locataire</value>
  </data>
  <data name="User Name" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="{0} session changed." xml:space="preserve">
    <value>La session {0} a été modifiée.</value>
  </data>
  <data name="Superior Name" xml:space="preserve">
    <value>Nom du supérieur</value>
  </data>
  <data name="Provider" xml:space="preserve">
    <value>Fournisseur</value>
  </data>
  <data name="Select Superior" xml:space="preserve">
    <value>Sélectionnez Supérieure</value>
  </data>
  <data name="E-mail" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="Confirm Password" xml:space="preserve">
    <value>Confirmez le mot de passe</value>
  </data>
  <data name="Display Name" xml:space="preserve">
    <value>Afficher un nom</value>
  </data>
  <data name="Set Permissions" xml:space="preserve">
    <value>Définir les autorisations</value>
  </data>
  <data name="Assigned Roles" xml:space="preserve">
    <value>Rôles assignés</value>
  </data>
  <data name="Email / PhoneNumber" xml:space="preserve">
    <value>E-mail/numéro de téléphone</value>
  </data>
  <data name="Permission removed successfully." xml:space="preserve">
    <value>Autorisation supprimée avec succès</value>
  </data>
  <data name="Reset Password" xml:space="preserve">
    <value>réinitialiser le mot de passe</value>
  </data>
  <data name="Display Name / PhoneNumber" xml:space="preserve">
    <value>Nom d'affichage/numéro de téléphone</value>
  </data>
  <data name="Permission assigned successfully." xml:space="preserve">
    <value>Autorisation attribuée avec succès</value>
  </data>
  <data name="Recovery email sent. Please check your inbox to set a new password." xml:space="preserve">
    <value>E-mail de récupération envoyé. Veuillez vérifier votre boîte de réception pour définir un nouveau mot de passe.</value>
  </data>
  <data name="The user has been activated." xml:space="preserve">
    <value>L'utilisateur a été activé.</value>
  </data>
  <data name="The user has been inactivated." xml:space="preserve">
    <value>L'utilisateur a été inactivé.</value>
  </data>
  <data name="New user created successfully." xml:space="preserve">
    <value>Nouvel utilisateur créé avec succès.</value>
  </data>
  <data name="The user updated successfully." xml:space="preserve">
    <value>L'utilisateur a mis à jour avec succès.</value>
  </data>
  <data name="Full Name" xml:space="preserve">
    <value>Nom et prénom</value>
  </data>
  <data name="We send a verification link to this email address. The link expires after 72 hours." xml:space="preserve">
    <value>Nous envoyons un lien de vérification à cette adresse e-mail. Le lien expire après 72 heures.</value>
  </data>
  <data name="Click to change status to active." xml:space="preserve">
    <value>Cliquez pour changer le statut en actif.</value>
  </data>
  <data name="You cannot delete your own account!" xml:space="preserve">
    <value>Vous ne pouvez pas supprimer votre propre compte !</value>
  </data>
  <data name="Verification email sent to {0}." xml:space="preserve">
    <value>E-mail de vérification envoyé à {0}.</value>
  </data>
  <data name="A new password for {0} has been sent via email. The user will be required to enter a new password upon initial login." xml:space="preserve">
    <value>Un nouveau mot de passe pour {0} a été envoyé par e-mail. L'utilisateur devra saisir un nouveau mot de passe lors de la connexion initiale.</value>
  </data>
  <data name="Authorization has been changed." xml:space="preserve">
    <value>L'autorisation a été modifiée.</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Click to change status to inactive." xml:space="preserve">
    <value>Cliquez pour changer le statut en inactif.</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Send Reset Password Email" xml:space="preserve">
    <value>Envoyer un e-mail de réinitialisation du mot de passe</value>
  </data>
  <data name="No permission to change status." xml:space="preserve">
    <value>Aucune autorisation de changer de statut.</value>
  </data>
  <data name="No roles available for this tenant." xml:space="preserve">
    <value>Aucun rôle n'est disponible pour ce locataire.</value>
  </data>
  <data name="Search by role name" xml:space="preserve">
    <value>Recherche par nom de rôle</value>
  </data>
  <data name="Tenant" xml:space="preserve">
    <value>Locataire</value>
  </data>
  <data name="Superior" xml:space="preserve">
    <value>Supérieur</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>Créé</value>
  </data>
  <data name="Created By" xml:space="preserve">
    <value>Créé par</value>
  </data>
  <data name="Last Modified" xml:space="preserve">
    <value>Dernière modification</value>
  </data>
  <data name="Last Modified By" xml:space="preserve">
    <value>Dernière modification par</value>
  </data>
  <data name="Time Zone" xml:space="preserve">
    <value>Fuseau horaire</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
</root>
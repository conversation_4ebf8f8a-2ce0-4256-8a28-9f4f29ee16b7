<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
        Microsoft ResX Schema 
        
        Version 2.0
        
        The primary goals of this format is to allow a simple XML format 
        that is mostly human readable. The generation and parsing of the 
        various data types are done through the TypeConverter classes 
        associated with the data types.
        
        Example:
        
        ... ado.net/XML headers & schema ...
        <resheader name="resmimetype">text/microsoft-resx</resheader>
        <resheader name="version">2.0</resheader>
        <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
        <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
        <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
        <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
        <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
            <value>[base64 mime encoded serialized .NET Framework object]</value>
        </data>
        <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
            <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
            <comment>This is a comment</comment>
        </data>
                    
        There are any number of "resheader" rows that contain simple 
        name/value pairs.
        
        Each data row contains a name, and value. The row also contains a 
        type or mimetype. Type corresponds to a .NET class that support 
        text/value conversion through the TypeConverter architecture. 
        Classes that don't support this are serialized and stored with the 
        mimetype set.
        
        The mimetype is used for serialized objects, and tells the 
        ResXResourceReader how to depersist the object. This is currently not 
        extensible. For a given mimetype the value must be set accordingly:
        
        Note - application/x-microsoft.net.object.binary.base64 is the format 
        that the ResXResourceWriter will generate, however the reader can 
        read any of the formats listed below.
        
        mimetype: application/x-microsoft.net.object.binary.base64
        value   : The object must be serialized with 
                : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
                : and then encoded with base64 encoding.
        
        mimetype: application/x-microsoft.net.object.soap.base64
        value   : The object must be serialized with 
                : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
                : and then encoded with base64 encoding.
    
        mimetype: application/x-microsoft.net.object.bytearray.base64
        value   : The object must be serialized into a byte array 
                : using a System.ComponentModel.TypeConverter
                : and then encoded with base64 encoding.
        -->
  <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root" xmlns="">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
  </resheader>
  <data name="Don't have an account?" xml:space="preserve">
    <value>没有账户？</value>
  </data>
  <data name="Forgot password?" xml:space="preserve">
    <value>忘记密码？</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Password is required!" xml:space="preserve">
    <value>密码是必需的！</value>
  </data>
  <data name="Password must be at least of length 6" xml:space="preserve">
    <value>密码长度必须至少为 6</value>
  </data>
  <data name="Password must contain at least one capital letter" xml:space="preserve">
    <value>密码必须包含至少一个大写字母</value>
  </data>
  <data name="Password must contain at least one lowercase letter" xml:space="preserve">
    <value>密码必须包含至少一个小写字母</value>
  </data>
  <data name="Password must contain at least one digit" xml:space="preserve">
    <value>密码必须包含至少一位数字</value>
  </data>
  <data name="Please check your username and password.  If you are still unable to log in, contact your administrator." xml:space="preserve">
    <value>请检查您的用户名和密码。 如果您仍然无法登录，请联系您的管理员。</value>
  </data>
  <data name="Remember me?" xml:space="preserve">
    <value>记住账号？</value>
  </data>
  <data name="Sign In" xml:space="preserve">
    <value>登入</value>
  </data>
  <data name="Sign Up" xml:space="preserve">
    <value>报名</value>
  </data>
  <data name="User name" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="User name is required!" xml:space="preserve">
    <value>用户名为必填项！</value>
  </data>
  <data name="Please check your username and password. If you are still unable to log in, contact your administrator." xml:space="preserve">
    <value>请检查您的用户名和密码。 如果您仍然无法登录，请联系您的管理员。</value>
  </data>
  <data name="No user found, or no authorization, please contact the administrator." xml:space="preserve">
    <value>未找到用户，或者没有授权，请联系管理员。</value>
  </data>
  <data name="Please check your username and password. If you are still unable to log in, contact an administrator." xml:space="preserve">
    <value>请检查您的用户名和密码。 如果仍然无法登录，请联系管理员。</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="Username is required!" xml:space="preserve">
    <value>需要用户名！</value>
  </data>
  <data name="No user found, or no authorization, please contact an administrator." xml:space="preserve">
    <value>未找到用户，或者没有授权，请联系管理员。</value>
  </data>
  <data name="Password update successfully" xml:space="preserve">
    <value>密码更新成功</value>
  </data>
  <data name="Set new password" xml:space="preserve">
    <value>设置新密码</value>
  </data>
  <data name="Error: Invalid login attempt." xml:space="preserve">
    <value>错误：登录尝试无效。</value>
  </data>
  <data name="Error: Your account is not allowed to log in. Please ensure your account has been activated and you have completed all required steps." xml:space="preserve">
    <value>错误：您的帐户不允许登录。请确保您的帐户已激活并且您已完成所有必需的步骤。</value>
  </data>
  <data name="Error: User does not exist." xml:space="preserve">
    <value>错误：用户不存在。</value>
  </data>
  <data name="Error: Your account is inactive. Please contact support." xml:space="preserve">
    <value>错误：您的帐户处于非活动状态。请联系支持人员。</value>
  </data>
</root>
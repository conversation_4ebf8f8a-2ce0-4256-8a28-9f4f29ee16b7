﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Blazor" xml:space="preserve">
    <value>Blazor</value>
  </data>
  <data name="Blazor Dashboard" xml:space="preserve">
    <value>Tableau de bord Blazor</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Entreprise</value>
  </data>
  <data name="@2024 Copyright" xml:space="preserve">
    <value>Droit d'auteur @2024</value>
  </data>
  <data name="version" xml:space="preserve">
    <value>version</value>
  </data>
  <data name="Coming Soon" xml:space="preserve">
    <value>Bientôt disponible</value>
  </data>
  <data name="WIP" xml:space="preserve">
    <value>WIP</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Complété</value>
  </data>
  <data name="Application" xml:space="preserve">
    <value>Application</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Accueil</value>
  </data>
  <data name="E-Commerce" xml:space="preserve">
    <value>Commerce électronique</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Produits</value>
  </data>
  <data name="Documents" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="Analytics" xml:space="preserve">
    <value>Analytiques</value>
  </data>
  <data name="Banking" xml:space="preserve">
    <value>Banque</value>
  </data>
  <data name="Booking" xml:space="preserve">
    <value>Réservation</value>
  </data>
  <data name="MANAGEMENT" xml:space="preserve">
    <value>GESTION</value>
  </data>
  <data name="Authorization" xml:space="preserve">
    <value>Autorisation</value>
  </data>
  <data name="Multi-Tenant" xml:space="preserve">
    <value>Multi-locataire</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Rôles</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>Système</value>
  </data>
  <data name="Picklist" xml:space="preserve">
    <value>Liste de sélection</value>
  </data>
  <data name="Audit Trails" xml:space="preserve">
    <value>Audit Trails</value>
  </data>
  <data name="Logs" xml:space="preserve">
    <value>Journaux</value>
  </data>
  <data name="Jobs" xml:space="preserve">
    <value>Jobs</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contacts</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="authentication is required, click" xml:space="preserve">
    <value>Une authentification est requise, cliquez sur</value>
  </data>
  <data name="authentication is required, click sign in." xml:space="preserve">
    <value>Une authentification est requise, cliquez sur s'identifier.</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Couleur</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirmer</value>
  </data>
  <data name="Delete Confirmation" xml:space="preserve">
    <value>Confirmation de suppression</value>
  </data>
  <data name="Go Home" xml:space="preserve">
    <value>Aller à la page d'accueil</value>
  </data>
  <data name="Logged out" xml:space="preserve">
    <value>Déconnecté</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Déconnexion</value>
  </data>
  <data name="Logout Confirmation" xml:space="preserve">
    <value>Confirmation de la déconnexion</value>
  </data>
  <data name="Mark as read" xml:space="preserve">
    <value>Marquer comme lu</value>
  </data>
  <data name="Mode" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="Nothing new :(" xml:space="preserve">
    <value>Rien de nouveau :(</value>
  </data>
  <data name="Notifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profil</value>
  </data>
  <data name="Search..." xml:space="preserve">
    <value>Rechercher...</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Paramètres</value>
  </data>
  <data name="sign in" xml:space="preserve">
    <value>connexion</value>
  </data>
  <data name="Sorry, there's nothing at this address." xml:space="preserve">
    <value>Désolé, il n'y a rien à cette adresse.</value>
  </data>
  <data name="Themes" xml:space="preserve">
    <value>Thèmes</value>
  </data>
  <data name="You are attempting to log out of application. Do you really want to log out?" xml:space="preserve">
    <value>Vous tentez de vous déconnecter de l'application. Voulez-vous vraiment vous déconnecter ?</value>
  </data>
  <data name="You are not authorized to be here. For more information, contact your system administrator." xml:space="preserve">
    <value>Vous n'êtes pas autorisé à être ici. Pour plus d'informations, contactez votre administrateur système.</value>
  </data>
  <data name="Notification" xml:space="preserve">
    <value>Notification</value>
  </data>
  <data name="User Profile" xml:space="preserve">
    <value>Profil de l'utilisateur</value>
  </data>
  <data name="{0} has logged in." xml:space="preserve">
    <value>{0} s'est connecté.</value>
  </data>
  <data name="{0} has logged out." xml:space="preserve">
    <value>{0} s'est déconnecté.</value>
  </data>
  <data name="You are not authorized to view this page." xml:space="preserve">
    <value>Vous n'êtes pas autorisé à consulter cette page.</value>
  </data>
  <data name="Please wait, we are authorizing you..." xml:space="preserve">
    <value>Veuillez patienter, nous vous autorisons...</value>
  </data>
  <data name="Contact Administrator" xml:space="preserve">
    <value>Contacter l'administrateur</value>
  </data>
</root>
﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace CleanArchitecture.Blazor.Infrastructure.Resources.Constants {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ConstantString {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ConstantString() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("CleanArchitecture.Blazor.Infrastructure.Resources.Constants.ConstantString", typeof(ConstantString).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actions.
        /// </summary>
        internal static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advanced Search.
        /// </summary>
        internal static string Advanced_Search {
            get {
                return ResourceManager.GetString("Advanced Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the selected items: {0}?.
        /// </summary>
        internal static string Are_you_sure_you_want_to_delete_the_selected_items___0__ {
            get {
                return ResourceManager.GetString("Are you sure you want to delete the selected items: {0}?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this item: {0}?.
        /// </summary>
        internal static string Are_you_sure_you_want_to_delete_this_item___0__ {
            get {
                return ResourceManager.GetString("Are you sure you want to delete this item: {0}?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this item with Id: {0}?.
        /// </summary>
        internal static string Are_you_sure_you_want_to_delete_this_item_with_Id___0__ {
            get {
                return ResourceManager.GetString("Are you sure you want to delete this item with Id: {0}?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        internal static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm.
        /// </summary>
        internal static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        internal static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a new {0}.
        /// </summary>
        internal static string Create_a_new__0_ {
            get {
                return ResourceManager.GetString("Create a new {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create successfully.
        /// </summary>
        internal static string Create_successfully {
            get {
                return ResourceManager.GetString("Create successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Confirmation.
        /// </summary>
        internal static string Delete_Confirmation {
            get {
                return ResourceManager.GetString("Delete Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete selected items: {0}.
        /// </summary>
        internal static string Delete_selected_items___0_ {
            get {
                return ResourceManager.GetString("Delete selected items: {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete successfully.
        /// </summary>
        internal static string Delete_successfully {
            get {
                return ResourceManager.GetString("Delete successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete the {0}.
        /// </summary>
        internal static string Delete_the__0_ {
            get {
                return ResourceManager.GetString("Delete the {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Downloading....
        /// </summary>
        internal static string Downloading___ {
            get {
                return ResourceManager.GetString("Downloading...", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the {0}.
        /// </summary>
        internal static string Edit_the__0_ {
            get {
                return ResourceManager.GetString("Edit the {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export fail.
        /// </summary>
        internal static string Export_fail {
            get {
                return ResourceManager.GetString("Export fail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export successfully.
        /// </summary>
        internal static string Export_successfully {
            get {
                return ResourceManager.GetString("Export successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export to Excel.
        /// </summary>
        internal static string Export_to_Excel {
            get {
                return ResourceManager.GetString("Export to Excel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import fail.
        /// </summary>
        internal static string Import_fail {
            get {
                return ResourceManager.GetString("Import fail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import from Excel.
        /// </summary>
        internal static string Import_from_Excel {
            get {
                return ResourceManager.GetString("Import from Excel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import successfully.
        /// </summary>
        internal static string Import_successfully {
            get {
                return ResourceManager.GetString("Import successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading....
        /// </summary>
        internal static string Loading___ {
            get {
                return ResourceManager.GetString("Loading...", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login fail.
        /// </summary>
        internal static string Login_fail {
            get {
                return ResourceManager.GetString("Login fail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login successfully.
        /// </summary>
        internal static string Login_successfully {
            get {
                return ResourceManager.GetString("Login successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout.
        /// </summary>
        internal static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout Confirmation.
        /// </summary>
        internal static string Logout_Confirmation {
            get {
                return ResourceManager.GetString("Logout Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout fail.
        /// </summary>
        internal static string Logout_fail {
            get {
                return ResourceManager.GetString("Logout fail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout successfully.
        /// </summary>
        internal static string Logout_successfully {
            get {
                return ResourceManager.GetString("Logout successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next.
        /// </summary>
        internal static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Allowed.
        /// </summary>
        internal static string No_Allowed {
            get {
                return ResourceManager.GetString("Not Allowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        internal static string OK {
            get {
                return ResourceManager.GetString("OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order By.
        /// </summary>
        internal static string Order_By {
            get {
                return ResourceManager.GetString("Order By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous.
        /// </summary>
        internal static string Previous {
            get {
                return ResourceManager.GetString("Previous", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        internal static string Refresh {
            get {
                return ResourceManager.GetString("Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        internal static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Changes.
        /// </summary>
        internal static string Save_Changes {
            get {
                return ResourceManager.GetString("Save Changes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save successfully.
        /// </summary>
        internal static string Save_successfully {
            get {
                return ResourceManager.GetString("Save successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        internal static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign in with {0}.
        /// </summary>
        internal static string Sign_in_with__0_ {
            get {
                return ResourceManager.GetString("Sign in with {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddOrUpdate successfully.
        /// </summary>
        internal static string Update_successfully {
            get {
                return ResourceManager.GetString("AddOrUpdate successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uploading....
        /// </summary>
        internal static string Uploading___ {
            get {
                return ResourceManager.GetString("Uploading...", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are attempting to log out of application. Do you really want to log out?.
        /// </summary>
        internal static string You_are_attempting_to_log_out_of_application__Do_you_really_want_to_log_out_ {
            get {
                return ResourceManager.GetString("You are attempting to log out of application. Do you really want to log out?", resourceCulture);
            }
        }
    }
}

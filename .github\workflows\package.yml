name: Package
on:
  push:
    branches:
      - main
    paths:
      - 'CleanArchitecture.Blazor.nuspec'
jobs:
  publish:
    name: Publish to NuGet.org
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Install Mono
        run: sudo apt-get update && sudo apt-get install -y mono-complete
        
      - uses: nuget/setup-nuget@v2
        with:
          nuget-version: '6.x'

      - name: Create the package
        run: nuget pack CleanArchitecture.Blazor.nuspec -NoDefaultExcludes
        
      - name: Publish the package
        run: nuget push *.nupkg -Source 'https://api.nuget.org/v3/index.json' -ApiKey ${{secrets.NUGET_API_KEY}} -SkipDuplicate

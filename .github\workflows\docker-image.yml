name: Docker Image CI

on:
  push:
    branches: [ "main" ]
    paths-ignore:
      - "deploy/**"
  pull_request:
    branches: [ "main" ]
    paths-ignore:
      - "deploy/**"

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - name: Check out the repo
      uses: actions/checkout@v4
      
    - name: Get next version
      uses: reecetech/version-increment@2024.10.1
      id: version
      with:
        scheme: semver
        increment: patch
        
    - run: git tag ${{ steps.version.outputs.version }}
    - run: git push origin ${{ steps.version.outputs.version }}    
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
          
   

    - name: Build and push
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: blazordevlab/cleanarchitectureblazorserver:${{ steps.version.outputs.version }}

    - name: Build and push with latest tag
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: blazordevlab/cleanarchitectureblazorserver:latest


        
    - name: Update version in docker-compose files
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        echo "Updating docker-compose files with version ${{ steps.version.outputs.version }}"
        sed -i 's#blazordevlab/cleanarchitectureblazorserver:[0-9a-zA-Z\.\-]*#blazordevlab/cleanarchitectureblazorserver:${{ steps.version.outputs.version }}#' deploy/docker-compose.yml
        sed -i 's#blazordevlab/cleanarchitectureblazorserver:[0-9a-zA-Z\.\-]*#blazordevlab/cleanarchitectureblazorserver:${{ steps.version.outputs.version }}#' deploy/docker-compose-db.yml

    - name: Create Pull Request
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      uses: peter-evans/create-pull-request@v4
      with:
        commit-message: "Update Docker Compose files to version ${{ steps.version.outputs.version }}"
        title: "Update Docker Compose to version ${{ steps.version.outputs.version }}"
        body: "Automatically updating Docker Compose version references to match the latest build version ${{ steps.version.outputs.version }}"
        branch: update-compose-version-${{ steps.version.outputs.version }}

          
     

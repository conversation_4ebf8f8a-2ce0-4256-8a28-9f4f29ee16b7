﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Graphite on roof" xml:space="preserve">
    <value>Graphite sur le toit</value>
  </data>
  <data name="Global Spread" xml:space="preserve">
    <value>Propagation mondiale</value>
  </data>
  <data name="Roentgen" xml:space="preserve">
    <value>Roentgen</value>
  </data>
  <data name="Uranium-235" xml:space="preserve">
    <value>Uranium-235</value>
  </data>
  <data name="See all 137 reviews" xml:space="preserve">
    <value>Voir les 137 avis</value>
  </data>
  <data name="This is the type of uranium used in the RBMK reactors." xml:space="preserve">
    <value>C'est le type d'uranium utilisé dans les réacteurs RBMK.</value>
  </data>
  <data name="Reactor Type" xml:space="preserve">
    <value>Type de réacteur</value>
  </data>
  <data name="Read More" xml:space="preserve">
    <value>En savoir plus</value>
  </data>
  <data name="Global scale" xml:space="preserve">
    <value>Échelle globale</value>
  </data>
  <data name="MudBlazor is growing quickly" xml:space="preserve">
    <value>MudBlazor se développe rapidement</value>
  </data>
  <data name="We are growing every day, developers from all over the world are using MudBlazor and are engaged with the community." xml:space="preserve">
    <value>Nous grandissons chaque jour, des développeurs du monde entier utilisent MudBlazor et sont engagés auprès de la communauté.</value>
  </data>
  <data name="We are dedicated to improving every aspect of MudBlazor to be your number one choice when looking for a Blazor component library." xml:space="preserve">
    <value>Nous nous engageons à améliorer chaque aspect de MudBlazor pour être votre choix numéro un lorsque vous recherchez une bibliothèque de composants Blazor.</value>
  </data>
  <data name="Join us and be part of the library’s success!" xml:space="preserve">
    <value>Rejoignez-nous et faites partie du succès de la bibliothèque !</value>
  </data>
  <data name="Total Downloads" xml:space="preserve">
    <value>Total des téléchargements</value>
  </data>
  <data name="GitHub Stars" xml:space="preserve">
    <value>Étoiles GitHub</value>
  </data>
  <data name="Contributors" xml:space="preserve">
    <value>Contributeurs</value>
  </data>
  <data name="Discord Members" xml:space="preserve">
    <value>Membres Discord</value>
  </data>
  <data name="Nr" xml:space="preserve">
    <value>N°</value>
  </data>
  <data name="Sign" xml:space="preserve">
    <value>Signe</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Molar mass" xml:space="preserve">
    <value>Masse molaire</value>
  </data>
</root>
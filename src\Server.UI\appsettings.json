﻿{
  "UseInMemoryDatabase": false,
  "DatabaseSettings": {
    "DBProvider": "sqlite",
    "ConnectionString": "Data Source=BlazorDashboardDb.db"
    //"DBProvider": "mssql",
    //"ConnectionString": "Server=(localdb)\\mssqllocaldb;Database=BlazorDashboardDb;Trusted_Connection=True;MultipleActiveResultSets=true;"
    //"DBProvider": "postgresql",
    //"ConnectionString": "Server=127.0.0.1;Database=BlazorDashboardDb;User Id=root;Password=root;Port=5432"
  },
  "AI": {
    "GEMINI_API_KEY": "your-gemini-api-key"
  },
  "Authentication": {
    "Microsoft": {
      "ClientId": "***",
      "ClientSecret": "***"
    },
    "Google": {
      "ClientId": "***",
      "ClientSecret": "***"
    },
    "Facebook": {
      "AppId": "***",
      "AppSecret": "***"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Error",
      "Microsoft.AspNetCore": "Error",
      "Microsoft.Hosting.Lifetime": "Error",
      "Microsoft.EntityFrameworkCore.Database.Command": "Error"
    }
  },
  "AppConfigurationSettings": {
    "ApplicationUrl": "https://architecture.blazorserver.com",
    "Version": "1.2.22",
    "App": "Blazor",
    "AppName": "Blazor Studio",
    "Company": "Company",
    "Copyright": "@2024 Copyright"
  },
  "AllowedHosts": "*",
  "SmtpClientOptions": {
    "Server": "",
    "Port": 25,
    "User": "",
    "Password": "",
    "UseSsl": false,
    "RequiresAuthentication": true,
    "PreferredEncoding": "",
    "UsePickupDirectory": false,
    "MailPickupDirectory": "",
    "SocketOptions": null,
    "DefaultFromEmail": "<EMAIL>"
  },
  "Minio": {
    "Endpoint": "minio.blazors.app:8443",
    "AccessKey": "your-access-key",
    "SecretKey": "your-secret-key",
    "BucketName": "files"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Warning",
        "System": "Warning",
        "System.Net.Http.HttpClient": "Warning"
      }
    },
    "Properties": {
      "Application": "BlazorApp",
      "Environment": "Development",
      "TargetFramework": "net9"
    },
    "WriteTo": [
      {
        "Name": "Seq",
        "Args": {
          "serverUrl": "http://***********:8082",
          "apiKey": "none",
          "restrictedToMinimumLevel": "Verbose"
        }
      }
    ]
  },
  "IdentitySettings": {
    "RequireDigit": false,
    "RequiredLength": 6,
    "MaxLength": 16,
    "RequireNonAlphanumeric": false,
    "RequireUpperCase": false,
    "RequireLowerCase": false,
    "DefaultLockoutTimeSpan": 30
  }
}
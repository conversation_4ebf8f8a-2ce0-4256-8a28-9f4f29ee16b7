version: '3.8'
services:
  blazorserverapp:
    image: blazordevlab/cleanarchitectureblazorserver:1.2.21
    environment:
      - UseInMemoryDatabase=${USE_IN_MEMORY_DATABASE}
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
      - ASPNETCORE_URLS=${ASPNETCORE_URLS}
      - ASPNETCORE_HTTP_PORTS=${ASPNETCORE_HTTP_PORTS}
      - ASPNETCORE_HTTPS_PORTS=${ASPNETCORE_HTTPS_PORTS}
      - AppConfigurationSettings__ApplicationUrl=${APP_URL}
      - AppConfigurationSettings__AppName=${APP_NAME}
      - DatabaseSettings__DBProvider=${DB_PROVIDER}
      - DatabaseSettings__ConnectionString=${DB_CONNECTION_STRING}
      - AI__GEMINI_API_KEY=${GEMINI_API_KEY}
      - SmtpClientOptions__User=${SMTP_USER}
      - SmtpClientOptions__Port=${SMTP_PORT}
      - SmtpClientOptions__Server=${SMTP_SERVER}
      - SmtpClientOptions__Password=${SMTP_PASSWORD}
      - SmtpClientOptions__DefaultFromEmail=${SMTP_DEFAULT_FROM}
      - Authentication__Microsoft__ClientId=${MS_CLIENT_ID}
      - Authentication__Microsoft__ClientSecret=${MS_CLIENT_SECRET}
      - Authentication__Google__ClientId=${GOOGLE_CLIENT_ID}
      - Authentication__Google__ClientSecret=${GOOGLE_CLIENT_SECRET}
      - Minio__Endpoint=${MINIO_ENDPOINT}
      - Minio__AccessKey=${MINIO_ACCESS_KEY}
      - Minio__SecretKey=${MINIO_SECRET_KEY}
      - Minio__BucketName=${MINIO_BUCKET}
    ports:
      - "8014:80"
      - "8015:443"
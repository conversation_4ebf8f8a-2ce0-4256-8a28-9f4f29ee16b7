<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
        Microsoft ResX Schema 
        
        Version 2.0
        
        The primary goals of this format is to allow a simple XML format 
        that is mostly human readable. The generation and parsing of the 
        various data types are done through the TypeConverter classes 
        associated with the data types.
        
        Example:
        
        ... ado.net/XML headers & schema ...
        <resheader name="resmimetype">text/microsoft-resx</resheader>
        <resheader name="version">2.0</resheader>
        <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
        <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
        <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
        <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
        <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
            <value>[base64 mime encoded serialized .NET Framework object]</value>
        </data>
        <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
            <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
            <comment>This is a comment</comment>
        </data>
                    
        There are any number of "resheader" rows that contain simple 
        name/value pairs.
        
        Each data row contains a name, and value. The row also contains a 
        type or mimetype. Type corresponds to a .NET class that support 
        text/value conversion through the TypeConverter architecture. 
        Classes that don't support this are serialized and stored with the 
        mimetype set.
        
        The mimetype is used for serialized objects, and tells the 
        ResXResourceReader how to depersist the object. This is currently not 
        extensible. For a given mimetype the value must be set accordingly:
        
        Note - application/x-microsoft.net.object.binary.base64 is the format 
        that the ResXResourceWriter will generate, however the reader can 
        read any of the formats listed below.
        
        mimetype: application/x-microsoft.net.object.binary.base64
        value   : The object must be serialized with 
                : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
                : and then encoded with base64 encoding.
        
        mimetype: application/x-microsoft.net.object.soap.base64
        value   : The object must be serialized with 
                : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
                : and then encoded with base64 encoding.
    
        mimetype: application/x-microsoft.net.object.bytearray.base64
        value   : The object must be serialized into a byte array 
                : using a System.ComponentModel.TypeConverter
                : and then encoded with base64 encoding.
        -->
  <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root" xmlns="">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
  </resheader>
  <data name="Logout Confirmation" xml:space="preserve">
    <value>Confirmation de la déconnexion</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="Advanced Search" xml:space="preserve">
    <value>Recherche avancée</value>
  </data>
  <data name="Are you sure you want to delete the selected items: {0}?" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer les éléments sélectionnés : {0} ?</value>
  </data>
  <data name="Are you sure you want to delete this item with Id: {0}?" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cet élément avec l'ID : {0} ?</value>
  </data>
  <data name="Are you sure you want to delete this item: {0}?" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cet élément : {0} ?</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="Create a new {0}" xml:space="preserve">
    <value>Créer un nouveau {0}</value>
  </data>
  <data name="Create successfully" xml:space="preserve">
    <value>Création réussie</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Delete Confirmation" xml:space="preserve">
    <value>Confirmation de suppression</value>
  </data>
  <data name="Delete selected items: {0}" xml:space="preserve">
    <value>Supprimer les éléments sélectionnés : {0}</value>
  </data>
  <data name="Delete successfully" xml:space="preserve">
    <value>Suppression réussie</value>
  </data>
  <data name="Delete the {0}" xml:space="preserve">
    <value>Supprimer le {0}</value>
  </data>
  <data name="Downloading..." xml:space="preserve">
    <value>Téléchargement...</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Edit the {0}" xml:space="preserve">
    <value>Modifier le {0}</value>
  </data>
  <data name="Export fail" xml:space="preserve">
    <value>Échec de l'exportation</value>
  </data>
  <data name="Export successfully" xml:space="preserve">
    <value>Exportation réussie</value>
  </data>
  <data name="Export to Excel" xml:space="preserve">
    <value>Exporter vers Excel</value>
  </data>
  <data name="Import fail" xml:space="preserve">
    <value>Échec de l'importation</value>
  </data>
  <data name="Import from Excel" xml:space="preserve">
    <value>Importer depuis Excel</value>
  </data>
  <data name="Import successfully" xml:space="preserve">
    <value>Importation réussie</value>
  </data>
  <data name="Loading..." xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="Login fail" xml:space="preserve">
    <value>Échec de la connexion</value>
  </data>
  <data name="Login successfully" xml:space="preserve">
    <value>Connexion réussie</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Déconnexion</value>
  </data>
  <data name="Logout fail" xml:space="preserve">
    <value>Échec de la déconnexion</value>
  </data>
  <data name="Logout successfully" xml:space="preserve">
    <value>Déconnexion réussie</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Suivant</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Not Allowed" xml:space="preserve">
    <value>Non autorisé</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Order By" xml:space="preserve">
    <value>Trier par</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="Save Changes" xml:space="preserve">
    <value>Enregistrer les modifications</value>
  </data>
  <data name="Save successfully" xml:space="preserve">
    <value>Enregistrement réussi</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="Sign in with {0}" xml:space="preserve">
    <value>Se connecter avec {0}</value>
  </data>
  <data name="Update successfully" xml:space="preserve">
    <value>Mise à jour réussie</value>
  </data>
  <data name="Uploading..." xml:space="preserve">
    <value>Téléversement...</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirmer</value>
  </data>
  <data name="You are attempting to log out of application. Do you really want to log out?" xml:space="preserve">
    <value>Vous êtes sur le point de vous déconnecter de l'application. Voulez-vous vraiment vous déconnecter ?</value>
  </data>
  <data name="Sign In" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="Export to PDF" xml:space="preserve">
    <value>Exporter en PDF</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Soumettre</value>
  </data>
  <data name="Clone" xml:space="preserve">
    <value>Cloner</value>
  </data>
  <data name="Save &amp; New" xml:space="preserve">
    <value>Enregistrer &amp; Nouveau</value>
  </data>
  <data name="Selected Total" xml:space="preserve">
    <value>Total sélectionné</value>
  </data>
  <data name="Selected" xml:space="preserve">
    <value>Sélectionné</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Imprimer</value>
  </data>
  <data name="More" xml:space="preserve">
    <value>Plus</value>
  </data>
</root>